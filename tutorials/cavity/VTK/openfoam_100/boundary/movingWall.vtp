<?xml version='1.0'?>
<!-- patch='movingWall' time='0.5' index='100' -->
<VTKFile type='PolyData' version='0.1' byte_order='LittleEndian' header_type='UInt64'>
  <PolyData>
    <FieldData>
      <DataArray type='Float32' Name='TimeValue' NumberOfTuples='1' format='binary'>
BAAAAAAAAAAAAAA/
      </DataArray>
    </FieldData>
    <Piece NumberOfPoints='42' NumberOfPolys='20'>
      <Points>
        <DataArray type='Float32' Name='Points' NumberOfComponents='3' format='binary'>
+AEAAAAAAAAAAAAAzczMPQAAAAAAAAAAzczMPQrXIzwK16M7zczMPQrXIzwK16M7zczMPQAAAAAK1yM8zczMPQrXIzwK1yM8zczMPQAAAACPwnU8zczMPQrXIzyPwnU8zczMPQAAAAAK16M8zczMPQrXIzwK16M8zczMPQAAAADNzMw8zczMPQrXIzzNzMw8zczMPQAAAACPwvU8zczMPQrXIzyPwvU8zczMPQAAAAApXA89zczMPQrXIzwpXA89zczMPQAAAAAK1yM9zczMPQrXIzwK1yM9zczMPQAAAADsUTg9zczMPQrXIzzsUTg9zczMPQAAAADNzEw9zczMPQrXIzzNzEw9zczMPQAAAACuR2E9zczMPQrXIzyuR2E9zczMPQAAAACPwnU9zczMPQrXIzyPwnU9zczMPQAAAAC4HoU9zczMPQrXIzy4HoU9zczMPQAAAAApXI89zczMPQrXIzwpXI89zczMPQAAAACamZk9zczMPQrXIzyamZk9zczMPQAAAAAK16M9zczMPQrXIzwK16M9zczMPQAAAAB7FK49zczMPQrXIzx7FK49zczMPQAAAADsUbg9zczMPQrXIzzsUbg9zczMPQAAAABcj8I9zczMPQrXIzxcj8I9zczMPQAAAADNzMw9zczMPQrXIzzNzMw9zczMPQAAAAA=
        </DataArray>
      </Points>
      <Polys>
        <DataArray type='Int32' Name='connectivity' format='binary'>
QAEAAAAAAAAAAAAAAQAAAAIAAAADAAAAAwAAAAIAAAAEAAAABQAAAAUAAAAEAAAABgAAAAcAAAAHAAAABgAAAAgAAAAJAAAACQAAAAgAAAAKAAAACwAAAAsAAAAKAAAADAAAAA0AAAANAAAADAAAAA4AAAAPAAAADwAAAA4AAAAQAAAAEQAAABEAAAAQAAAAEgAAABMAAAATAAAAEgAAABQAAAAVAAAAFQAAABQAAAAWAAAAFwAAABcAAAAWAAAAGAAAABkAAAAZAAAAGAAAABoAAAAbAAAAGwAAABoAAAAcAAAAHQAAAB0AAAAcAAAAHgAAAB8AAAAfAAAAHgAAACAAAAAhAAAAIQAAACAAAAAiAAAAIwAAACMAAAAiAAAAJAAAACUAAAAlAAAAJAAAACYAAAAnAAAAJwAAACYAAAAoAAAAKQAAAA==
        </DataArray>
        <DataArray type='Int32' Name='offsets' format='binary'>
UAAAAAAAAAAEAAAACAAAAAwAAAAQAAAAFAAAABgAAAAcAAAAIAAAACQAAAAoAAAALAAAADAAAAA0AAAAOAAAADwAAABAAAAARAAAAEgAAABMAAAAUAAAAA==
        </DataArray>
      </Polys>
      <CellData>
        <DataArray type='Float32' Name='mag(U)' format='binary'>
UAAAAAAAAAAAAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPw==
        </DataArray>
        <DataArray type='Float32' Name='p' format='binary'>
UAAAAAAAAACuu4vAONsCwMk5eb9eLBC/ETe3vgzodb6gMi6+2H/9vSQns70BKVu9X2WIvFei4jwpSa09mZ0lPhx6iz5TW+I+dt03PxHknD97ThpAPSebQA==
        </DataArray>
        <DataArray type='Float32' Name='U' NumberOfComponents='3' format='binary'>
8AAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAA=
        </DataArray>
      </CellData>
      <PointData>
        <DataArray type='Float32' Name='mag(U)' format='binary'>
qAAAAAAAAAAAAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8AAIA/AACAPwAAgD8=
        </DataArray>
        <DataArray type='Float32' Name='p' format='binary'>
qAAAAAAAAACuu4vArruLwEopTcBKKU3AqinBv6opwb8Us0S/FLNEv+fH677nx+u+jBWZvowVmb5WDVK+Vg1SvkZ5Fr5GeRa+flPYvX5T2L3SXZC90l2QvditD73YrQ+98Hm0O/B5tDu/8WU9v/FlPS5C/D0uQvw96UhePulIXj646rY+uOq2PpCFFD+QhRQ/zNJ4P8zSeD+DwOg/g8DoP3tOaEB7TmhAPSebQD0nm0A=
        </DataArray>
        <DataArray type='Float32' Name='U' NumberOfComponents='3' format='binary'>
+AEAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAAAAIA/AAAAAAAAAAA=
        </DataArray>
      </PointData>
    </Piece>
  </PolyData>
</VTKFile>
