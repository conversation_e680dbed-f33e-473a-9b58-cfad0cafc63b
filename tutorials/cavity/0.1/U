/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2412                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volVectorField;
    location    "0.1";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   nonuniform List<vector> 
400
(
(0.000249136 -0.000245641 0)
(0.000137297 0.000111667 0)
(-0.00116243 0.000558043 0)
(-0.00343171 0.000872759 0)
(-0.00628217 0.00103011 0)
(-0.00932353 0.00104524 0)
(-0.012208 0.000942603 0)
(-0.0146482 0.000748202 0)
(-0.0164225 0.000486721 0)
(-0.0173758 0.000181792 0)
(-0.0174205 -0.000142538 0)
(-0.0165407 -0.000460362 0)
(-0.0147979 -0.000742949 0)
(-0.0123365 -0.000958733 0)
(-0.00938798 -0.00107452 0)
(-0.00626732 -0.00105852 0)
(-0.00335775 -0.000886232 0)
(-0.00107609 -0.000550229 0)
(0.000190716 -9.08709e-05 0)
(0.000263339 0.000257842 0)
(-7.60137e-05 -0.000165194 0)
(-0.00158574 0.00162161 0)
(-0.00560185 0.00356284 0)
(-0.01153 0.00495535 0)
(-0.0185846 0.00564958 0)
(-0.0259664 0.00566416 0)
(-0.032914 0.00508432 0)
(-0.0387696 0.00402325 0)
(-0.0430136 0.00260367 0)
(-0.0452802 0.000952857 0)
(-0.0453662 -0.000795728 0)
(-0.0432419 -0.00249956 0)
(-0.0390616 -0.0040059 0)
(-0.0331707 -0.00515499 0)
(-0.0261054 -0.00578939 0)
(-0.0185734 -0.00577245 0)
(-0.0114007 -0.00501896 0)
(-0.00543817 -0.00354003 0)
(-0.00147488 -0.00154077 0)
(-4.30717e-05 0.000221739 0)
(-0.000519324 0.00107409 0)
(-0.00364987 0.00564597 0)
(-0.0104162 0.0100618 0)
(-0.0196663 0.0131893 0)
(-0.0302624 0.0146977 0)
(-0.0411359 0.0145881 0)
(-0.0512608 0.0130298 0)
(-0.0597405 0.0102785 0)
(-0.0658633 0.00663045 0)
(-0.069126 0.00240493 0)
(-0.0692505 -0.00205902 0)
(-0.0661974 -0.00640076 0)
(-0.0601793 -0.0102397 0)
(-0.0516689 -0.0131866 0)
(-0.0413951 -0.0148695 0)
(-0.0303157 -0.0149807 0)
(-0.0195419 -0.0133483 0)
(-0.0102178 -0.0100316 0)
(-0.00350029 -0.00547612 0)
(-0.000471 -0.000938849 0)
(-0.000913011 0.00342246 0)
(-0.00560162 0.0120257 0)
(-0.015064 0.0196034 0)
(-0.0274846 0.0247891 0)
(-0.0413345 0.027142 0)
(-0.0553053 0.0266844 0)
(-0.0681716 0.023704 0)
(-0.0788736 0.0186372 0)
(-0.0865753 0.0119983 0)
(-0.090687 0.00434686 0)
(-0.09088 -0.00372082 0)
(-0.0871019 -0.0115719 0)
(-0.0795917 -0.0185418 0)
(-0.0688922 -0.0239546 0)
(-0.055851 -0.0271685 0)
(-0.041599 -0.0276541 0)
(-0.0274719 -0.0251096 0)
(-0.0148894 -0.0196033 0)
(-0.00543166 -0.0117632 0)
(-0.000850487 -0.00318351 0)
(-0.00126011 0.00676772 0)
(-0.00742881 0.0206279 0)
(-0.0195273 0.0320048 0)
(-0.035039 0.0394717 0)
(-0.0520065 0.0425987 0)
(-0.0688792 0.041511 0)
(-0.0842612 0.036672 0)
(-0.0969745 0.0287418 0)
(-0.106105 0.0184852 0)
(-0.11101 0.00672895 0)
(-0.111328 -0.00564903 0)
(-0.106984 -0.0177189 0)
(-0.098216 -0.028503 0)
(-0.0855879 -0.0370014 0)
(-0.0700134 -0.042254 0)
(-0.0527501 -0.0434486 0)
(-0.0353381 -0.040079 0)
(-0.0194928 -0.0321333 0)
(-0.00728075 -0.0203124 0)
(-0.00118919 -0.00641385 0)
(-0.00158757 0.0110466 0)
(-0.00920578 0.0314055 0)
(-0.0239218 0.0472447 0)
(-0.0424933 0.0572046 0)
(-0.0625054 0.0609982 0)
(-0.0821595 0.0589622 0)
(-0.0999097 0.051816 0)
(-0.114496 0.0404958 0)
(-0.124964 0.0260475 0)
(-0.13065 0.00958022 0)
(-0.131169 -0.00774078 0)
(-0.126418 -0.0246837 0)
(-0.116601 -0.0399452 0)
(-0.10226 -0.0521727 0)
(-0.0843217 -0.0600347 0)
(-0.0641308 -0.0623554 0)
(-0.0434178 -0.0583163 0)
(-0.0242208 -0.0476989 0)
(-0.00915772 -0.0311525 0)
(-0.00152183 -0.010595 0)
(-0.0019214 0.0162497 0)
(-0.0110167 0.0443644 0)
(-0.0283838 0.0653302 0)
(-0.0500091 0.0779645 0)
(-0.0729924 0.0822701 0)
(-0.0952904 0.0789287 0)
(-0.115237 0.069015 0)
(-0.131533 0.0538031 0)
(-0.143231 0.0346481 0)
(-0.149681 0.0129434 0)
(-0.150492 -0.00987169 0)
(-0.145524 -0.0322802 0)
(-0.134913 -0.0526595 0)
(-0.119126 -0.0692865 0)
(-0.0990417 -0.0804038 0)
(-0.0760333 -0.0843716 0)
(-0.0519932 -0.0799162 0)
(-0.0292972 -0.0664461 0)
(-0.0111897 -0.0444064 0)
(-0.00188446 -0.0157677 0)
(-0.00228492 0.0224165 0)
(-0.0129402 0.0595684 0)
(-0.0330346 0.0862899 0)
(-0.0576975 0.101704 0)
(-0.0835115 0.106283 0)
(-0.10821 0.101219 0)
(-0.130057 0.0880626 0)
(-0.147786 0.0684954 0)
(-0.160517 0.0442035 0)
(-0.167665 0.0168499 0)
(-0.168857 -0.0118892 0)
(-0.163914 -0.0402551 0)
(-0.152868 -0.0663387 0)
(-0.136045 -0.0880481 0)
(-0.114185 -0.10315 0)
(-0.0886027 -0.109427 0)
(-0.0612836 -0.104966 0)
(-0.034932 -0.08858 0)
(-0.0135032 -0.0602985 0)
(-0.00231246 -0.0220471 0)
(-0.002702 0.0296307 0)
(-0.0150533 0.077133 0)
(-0.0379738 0.110155 0)
(-0.065597 0.128308 0)
(-0.0939531 0.132779 0)
(-0.120601 0.12548 0)
(-0.143828 0.108587 0)
(-0.162498 0.0842606 0)
(-0.175897 0.0545294 0)
(-0.183572 0.021289 0)
(-0.185213 -0.0136144 0)
(-0.180603 -0.0482594 0)
(-0.169635 -0.0805196 0)
(-0.152411 -0.107968 0)
(-0.129414 -0.127867 0)
(-0.101759 -0.137296 0)
(-0.0713966 -0.133483 0)
(-0.0413009 -0.114333 0)
(-0.0162237 -0.0791474 0)
(-0.00284368 -0.0296203 0)
(-0.00320494 0.0380236 0)
(-0.017449 0.0972174 0)
(-0.0432936 0.136918 0)
(-0.0736668 0.15752 0)
(-0.104019 0.161271 0)
(-0.131834 0.151088 0)
(-0.155571 0.129943 0)
(-0.174371 0.10055 0)
(-0.187817 0.0652701 0)
(-0.195685 0.0261655 0)
(-0.197787 -0.0148525 0)
(-0.193884 -0.0558221 0)
(-0.183699 -0.094517 0)
(-0.167016 -0.12826 0)
(-0.143914 -0.153818 0)
(-0.115098 -0.167463 0)
(-0.0822745 -0.165295 0)
(-0.0485355 -0.143895 0)
(-0.0194882 -0.101344 0)
(-0.00352627 -0.0387501 0)
(-0.00384888 0.0477944 0)
(-0.0202701 0.120023 0)
(-0.0491011 0.166478 0)
(-0.0817723 0.188831 0)
(-0.113165 0.190911 0)
(-0.140876 0.177008 0)
(-0.16376 0.151087 0)
(-0.181447 0.116475 0)
(-0.193976 0.0758217 0)
(-0.201475 0.0312482 0)
(-0.20395 -0.0154154 0)
(-0.201176 -0.062337 0)
(-0.192687 -0.107368 0)
(-0.177868 -0.147734 0)
(-0.156211 -0.179791 0)
(-0.127752 -0.198942 0)
(-0.093613 -0.19988 0)
(-0.0567081 -0.177306 0)
(-0.0234671 -0.127325 0)
(-0.00443259 -0.0497929 0)
(-0.00473771 0.0592606 0)
(-0.0237585 0.145798 0)
(-0.0555362 0.198547 0)
(-0.0896326 0.221322 0)
(-0.12048 0.220306 0)
(-0.146125 0.20163 0)
(-0.166154 0.17045 0)
(-0.180949 0.130711 0)
(-0.191189 0.0852583 0)
(-0.197474 0.0361151 0)
(-0.20009 -0.0151584 0)
(-0.198875 -0.0670743 0)
(-0.193187 -0.117798 0)
(-0.181957 -0.1647 0)
(-0.163934 -0.20392 0)
(-0.138164 -0.230045 0)
(-0.104713 -0.236115 0)
(-0.0657893 -0.214277 0)
(-0.0283948 -0.157528 0)
(-0.00568385 -0.0632411 0)
(-0.00607004 0.0729615 0)
(-0.0283229 0.174842 0)
(-0.0627449 0.232489 0)
(-0.0966576 0.253415 0)
(-0.124425 0.247277 0)
(-0.145123 0.222585 0)
(-0.159532 0.185811 0)
(-0.169074 0.141427 0)
(-0.175236 0.0922885 0)
(-0.179179 0.0401129 0)
(-0.181525 -0.0140272 0)
(-0.182242 -0.0692271 0)
(-0.180566 -0.124244 0)
(-0.175004 -0.176923 0)
(-0.163471 -0.223534 0)
(-0.14373 -0.258095 0)
(-0.114199 -0.271925 0)
(-0.0755286 -0.253899 0)
(-0.0346032 -0.192299 0)
(-0.00749291 -0.0798093 0)
(-0.00821429 0.0898383 0)
(-0.0346013 0.207432 0)
(-0.0707037 0.266977 0)
(-0.101523 0.282468 0)
(-0.122291 0.26855 0)
(-0.134034 0.236575 0)
(-0.139276 0.194255 0)
(-0.140726 0.146306 0)
(-0.140734 0.09529 0)
(-0.14101 0.0423627 0)
(-0.142513 -0.012106 0)
(-0.145387 -0.0680087 0)
(-0.148872 -0.124968 0)
(-0.151198 -0.181686 0)
(-0.149542 -0.235076 0)
(-0.140246 -0.279145 0)
(-0.119485 -0.303793 0)
(-0.0851434 -0.294126 0)
(-0.0425149 -0.231673 0)
(-0.0102274 -0.100573 0)
(-0.0117902 0.111494 0)
(-0.0433838 0.243525 0)
(-0.0786071 0.299332 0)
(-0.101167 0.304184 0)
(-0.109136 0.279433 0)
(-0.106758 0.239338 0)
(-0.0987792 0.192321 0)
(-0.0892057 0.142768 0)
(-0.0810688 0.0925062 0)
(-0.076423 0.0418641 0)
(-0.0764405 -0.00964265 0)
(-0.081457 -0.0628186 0)
(-0.0908909 -0.118328 0)
(-0.103021 -0.176082 0)
(-0.114683 -0.234273 0)
(-0.121045 -0.287822 0)
(-0.11575 -0.326186 0)
(-0.0925298 -0.330943 0)
(-0.0524124 -0.27482 0)
(-0.0144458 -0.127128 0)
(-0.0175686 0.140581 0)
(-0.0548762 0.28186 0)
(-0.0830737 0.324225 0)
(-0.0885925 0.311808 0)
(-0.0758703 0.273683 0)
(-0.0536082 0.225971 0)
(-0.0287161 0.176547 0)
(-0.00597744 0.128526 0)
(0.0114834 0.0824956 0)
(0.0217383 0.0377586 0)
(0.0236848 -0.00701931 0)
(0.0168578 -0.0534711 0)
(0.00146598 -0.10326 0)
(-0.0213134 -0.157669 0)
(-0.0487593 -0.216748 0)
(-0.07579 -0.277569 0)
(-0.0940833 -0.331035 0)
(-0.0923581 -0.357042 0)
(-0.0633907 -0.318853 0)
(-0.020652 -0.161809 0)
(-0.0254621 0.181685 0)
(-0.0658574 0.317617 0)
(-0.073561 0.331059 0)
(-0.0485021 0.294966 0)
(-0.00623416 0.243691 0)
(0.0403175 0.191833 0)
(0.083515 0.144534 0)
(0.119192 0.102548 0)
(0.1452 0.0648466 0)
(0.160452 0.0297581 0)
(0.164326 -0.00463853 0)
(0.156373 -0.040437 0)
(0.136293 -0.0799182 0)
(0.104183 -0.125484 0)
(0.0612316 -0.179227 0)
(0.011027 -0.241497 0)
(-0.0383537 -0.307379 0)
(-0.0719699 -0.35972 0)
(-0.0701659 -0.356318 0)
(-0.0279312 -0.20827 0)
(-0.0296523 0.239182 0)
(-0.060885 0.335708 0)
(-0.022087 0.300352 0)
(0.0502452 0.239892 0)
(0.127085 0.182656 0)
(0.196309 0.135076 0)
(0.253428 0.0971043 0)
(0.297157 0.0666083 0)
(0.327588 0.0411894 0)
(0.345145 0.018704 0)
(0.350032 -0.00277366 0)
(0.342002 -0.0251555 0)
(0.32027 -0.0506027 0)
(0.283596 -0.081807 0)
(0.230714 -0.122171 0)
(0.161531 -0.175385 0)
(0.0794622 -0.243162 0)
(-0.00343385 -0.31891 0)
(-0.054762 -0.368461 0)
(-0.028475 -0.267568 0)
(0.00808789 0.281877 0)
(0.0197772 0.294965 0)
(0.139007 0.210925 0)
(0.264305 0.14339 0)
(0.36408 0.0970723 0)
(0.439923 0.0656541 0)
(0.496149 0.0441215 0)
(0.536143 0.0287917 0)
(0.562612 0.0171363 0)
(0.577409 0.00745924 0)
(0.581469 -0.00148199 0)
(0.574817 -0.0107988 0)
(0.556504 -0.0217726 0)
(0.524437 -0.0362408 0)
(0.47521 -0.0571548 0)
(0.404112 -0.089278 0)
(0.304893 -0.139643 0)
(0.173262 -0.216306 0)
(0.0411171 -0.311605 0)
(0.0184228 -0.299985 0)
(0.299611 0.146098 0)
(0.396286 0.127507 0)
(0.557422 0.076085 0)
(0.674454 0.0437097 0)
(0.743801 0.0255525 0)
(0.787 0.0152204 0)
(0.815267 0.00919216 0)
(0.83368 0.0054937 0)
(0.845069 0.00301772 0)
(0.851032 0.00114688 0)
(0.852325 -0.000495083 0)
(0.849058 -0.00220495 0)
(0.840708 -0.00432407 0)
(0.825894 -0.00740686 0)
(0.801884 -0.012514 0)
(0.763481 -0.021755 0)
(0.699021 -0.039267 0)
(0.583912 -0.0726478 0)
(0.415819 -0.127862 0)
(0.308822 -0.14947 0)
)
;

boundaryField
{
    movingWall
    {
        type            fixedValue;
        value           uniform (1 0 0);
    }
    fixedWalls
    {
        type            fixedValue;
        value           uniform (0 0 0);
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
