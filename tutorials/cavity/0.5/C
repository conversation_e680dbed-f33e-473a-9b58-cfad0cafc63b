/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2412                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volVectorField;
    location    "0.5";
    object      C;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 0 0 0 0 0];

internalField   nonuniform List<vector> 
400
(
(0.0025 0.0025 0.005)
(0.0075 0.0025 0.005)
(0.0125 0.0025 0.005)
(0.0175 0.0025 0.005)
(0.0225 0.0025 0.005)
(0.0275 0.0025 0.005)
(0.0325 0.0025 0.005)
(0.0375 0.0025 0.005)
(0.0425 0.0025 0.005)
(0.0475 0.0025 0.005)
(0.0525 0.0025 0.005)
(0.0575 0.0025 0.005)
(0.0625 0.0025 0.005)
(0.0675 0.0025 0.005)
(0.0725 0.0025 0.005)
(0.0775 0.0025 0.005)
(0.0825 0.0025 0.005)
(0.0875 0.0025 0.005)
(0.0925 0.0025 0.005)
(0.0975 0.0025 0.005)
(0.0025 0.0075 0.005)
(0.0075 0.0075 0.005)
(0.0125 0.0075 0.005)
(0.0175 0.0075 0.005)
(0.0225 0.0075 0.005)
(0.0275 0.0075 0.005)
(0.0325 0.0075 0.005)
(0.0375 0.0075 0.005)
(0.0425 0.0075 0.005)
(0.0475 0.0075 0.005)
(0.0525 0.0075 0.005)
(0.0575 0.0075 0.005)
(0.0625 0.0075 0.005)
(0.0675 0.0075 0.005)
(0.0725 0.0075 0.005)
(0.0775 0.0075 0.005)
(0.0825 0.0075 0.005)
(0.0875 0.0075 0.005)
(0.0925 0.0075 0.005)
(0.0975 0.0075 0.005)
(0.0025 0.0125 0.005)
(0.0075 0.0125 0.005)
(0.0125 0.0125 0.005)
(0.0175 0.0125 0.005)
(0.0225 0.0125 0.005)
(0.0275 0.0125 0.005)
(0.0325 0.0125 0.005)
(0.0375 0.0125 0.005)
(0.0425 0.0125 0.005)
(0.0475 0.0125 0.005)
(0.0525 0.0125 0.005)
(0.0575 0.0125 0.005)
(0.0625 0.0125 0.005)
(0.0675 0.0125 0.005)
(0.0725 0.0125 0.005)
(0.0775 0.0125 0.005)
(0.0825 0.0125 0.005)
(0.0875 0.0125 0.005)
(0.0925 0.0125 0.005)
(0.0975 0.0125 0.005)
(0.0025 0.0175 0.005)
(0.0075 0.0175 0.005)
(0.0125 0.0175 0.005)
(0.0175 0.0175 0.005)
(0.0225 0.0175 0.005)
(0.0275 0.0175 0.005)
(0.0325 0.0175 0.005)
(0.0375 0.0175 0.005)
(0.0425 0.0175 0.005)
(0.0475 0.0175 0.005)
(0.0525 0.0175 0.005)
(0.0575 0.0175 0.005)
(0.0625 0.0175 0.005)
(0.0675 0.0175 0.005)
(0.0725 0.0175 0.005)
(0.0775 0.0175 0.005)
(0.0825 0.0175 0.005)
(0.0875 0.0175 0.005)
(0.0925 0.0175 0.005)
(0.0975 0.0175 0.005)
(0.0025 0.0225 0.005)
(0.0075 0.0225 0.005)
(0.0125 0.0225 0.005)
(0.0175 0.0225 0.005)
(0.0225 0.0225 0.005)
(0.0275 0.0225 0.005)
(0.0325 0.0225 0.005)
(0.0375 0.0225 0.005)
(0.0425 0.0225 0.005)
(0.0475 0.0225 0.005)
(0.0525 0.0225 0.005)
(0.0575 0.0225 0.005)
(0.0625 0.0225 0.005)
(0.0675 0.0225 0.005)
(0.0725 0.0225 0.005)
(0.0775 0.0225 0.005)
(0.0825 0.0225 0.005)
(0.0875 0.0225 0.005)
(0.0925 0.0225 0.005)
(0.0975 0.0225 0.005)
(0.0025 0.0275 0.005)
(0.0075 0.0275 0.005)
(0.0125 0.0275 0.005)
(0.0175 0.0275 0.005)
(0.0225 0.0275 0.005)
(0.0275 0.0275 0.005)
(0.0325 0.0275 0.005)
(0.0375 0.0275 0.005)
(0.0425 0.0275 0.005)
(0.0475 0.0275 0.005)
(0.0525 0.0275 0.005)
(0.0575 0.0275 0.005)
(0.0625 0.0275 0.005)
(0.0675 0.0275 0.005)
(0.0725 0.0275 0.005)
(0.0775 0.0275 0.005)
(0.0825 0.0275 0.005)
(0.0875 0.0275 0.005)
(0.0925 0.0275 0.005)
(0.0975 0.0275 0.005)
(0.0025 0.0325 0.005)
(0.0075 0.0325 0.005)
(0.0125 0.0325 0.005)
(0.0175 0.0325 0.005)
(0.0225 0.0325 0.005)
(0.0275 0.0325 0.005)
(0.0325 0.0325 0.005)
(0.0375 0.0325 0.005)
(0.0425 0.0325 0.005)
(0.0475 0.0325 0.005)
(0.0525 0.0325 0.005)
(0.0575 0.0325 0.005)
(0.0625 0.0325 0.005)
(0.0675 0.0325 0.005)
(0.0725 0.0325 0.005)
(0.0775 0.0325 0.005)
(0.0825 0.0325 0.005)
(0.0875 0.0325 0.005)
(0.0925 0.0325 0.005)
(0.0975 0.0325 0.005)
(0.0025 0.0375 0.005)
(0.0075 0.0375 0.005)
(0.0125 0.0375 0.005)
(0.0175 0.0375 0.005)
(0.0225 0.0375 0.005)
(0.0275 0.0375 0.005)
(0.0325 0.0375 0.005)
(0.0375 0.0375 0.005)
(0.0425 0.0375 0.005)
(0.0475 0.0375 0.005)
(0.0525 0.0375 0.005)
(0.0575 0.0375 0.005)
(0.0625 0.0375 0.005)
(0.0675 0.0375 0.005)
(0.0725 0.0375 0.005)
(0.0775 0.0375 0.005)
(0.0825 0.0375 0.005)
(0.0875 0.0375 0.005)
(0.0925 0.0375 0.005)
(0.0975 0.0375 0.005)
(0.0025 0.0425 0.005)
(0.0075 0.0425 0.005)
(0.0125 0.0425 0.005)
(0.0175 0.0425 0.005)
(0.0225 0.0425 0.005)
(0.0275 0.0425 0.005)
(0.0325 0.0425 0.005)
(0.0375 0.0425 0.005)
(0.0425 0.0425 0.005)
(0.0475 0.0425 0.005)
(0.0525 0.0425 0.005)
(0.0575 0.0425 0.005)
(0.0625 0.0425 0.005)
(0.0675 0.0425 0.005)
(0.0725 0.0425 0.005)
(0.0775 0.0425 0.005)
(0.0825 0.0425 0.005)
(0.0875 0.0425 0.005)
(0.0925 0.0425 0.005)
(0.0975 0.0425 0.005)
(0.0025 0.0475 0.005)
(0.0075 0.0475 0.005)
(0.0125 0.0475 0.005)
(0.0175 0.0475 0.005)
(0.0225 0.0475 0.005)
(0.0275 0.0475 0.005)
(0.0325 0.0475 0.005)
(0.0375 0.0475 0.005)
(0.0425 0.0475 0.005)
(0.0475 0.0475 0.005)
(0.0525 0.0475 0.005)
(0.0575 0.0475 0.005)
(0.0625 0.0475 0.005)
(0.0675 0.0475 0.005)
(0.0725 0.0475 0.005)
(0.0775 0.0475 0.005)
(0.0825 0.0475 0.005)
(0.0875 0.0475 0.005)
(0.0925 0.0475 0.005)
(0.0975 0.0475 0.005)
(0.0025 0.0525 0.005)
(0.0075 0.0525 0.005)
(0.0125 0.0525 0.005)
(0.0175 0.0525 0.005)
(0.0225 0.0525 0.005)
(0.0275 0.0525 0.005)
(0.0325 0.0525 0.005)
(0.0375 0.0525 0.005)
(0.0425 0.0525 0.005)
(0.0475 0.0525 0.005)
(0.0525 0.0525 0.005)
(0.0575 0.0525 0.005)
(0.0625 0.0525 0.005)
(0.0675 0.0525 0.005)
(0.0725 0.0525 0.005)
(0.0775 0.0525 0.005)
(0.0825 0.0525 0.005)
(0.0875 0.0525 0.005)
(0.0925 0.0525 0.005)
(0.0975 0.0525 0.005)
(0.0025 0.0575 0.005)
(0.0075 0.0575 0.005)
(0.0125 0.0575 0.005)
(0.0175 0.0575 0.005)
(0.0225 0.0575 0.005)
(0.0275 0.0575 0.005)
(0.0325 0.0575 0.005)
(0.0375 0.0575 0.005)
(0.0425 0.0575 0.005)
(0.0475 0.0575 0.005)
(0.0525 0.0575 0.005)
(0.0575 0.0575 0.005)
(0.0625 0.0575 0.005)
(0.0675 0.0575 0.005)
(0.0725 0.0575 0.005)
(0.0775 0.0575 0.005)
(0.0825 0.0575 0.005)
(0.0875 0.0575 0.005)
(0.0925 0.0575 0.005)
(0.0975 0.0575 0.005)
(0.0025 0.0625 0.005)
(0.0075 0.0625 0.005)
(0.0125 0.0625 0.005)
(0.0175 0.0625 0.005)
(0.0225 0.0625 0.005)
(0.0275 0.0625 0.005)
(0.0325 0.0625 0.005)
(0.0375 0.0625 0.005)
(0.0425 0.0625 0.005)
(0.0475 0.0625 0.005)
(0.0525 0.0625 0.005)
(0.0575 0.0625 0.005)
(0.0625 0.0625 0.005)
(0.0675 0.0625 0.005)
(0.0725 0.0625 0.005)
(0.0775 0.0625 0.005)
(0.0825 0.0625 0.005)
(0.0875 0.0625 0.005)
(0.0925 0.0625 0.005)
(0.0975 0.0625 0.005)
(0.0025 0.0675 0.005)
(0.0075 0.0675 0.005)
(0.0125 0.0675 0.005)
(0.0175 0.0675 0.005)
(0.0225 0.0675 0.005)
(0.0275 0.0675 0.005)
(0.0325 0.0675 0.005)
(0.0375 0.0675 0.005)
(0.0425 0.0675 0.005)
(0.0475 0.0675 0.005)
(0.0525 0.0675 0.005)
(0.0575 0.0675 0.005)
(0.0625 0.0675 0.005)
(0.0675 0.0675 0.005)
(0.0725 0.0675 0.005)
(0.0775 0.0675 0.005)
(0.0825 0.0675 0.005)
(0.0875 0.0675 0.005)
(0.0925 0.0675 0.005)
(0.0975 0.0675 0.005)
(0.0025 0.0725 0.005)
(0.0075 0.0725 0.005)
(0.0125 0.0725 0.005)
(0.0175 0.0725 0.005)
(0.0225 0.0725 0.005)
(0.0275 0.0725 0.005)
(0.0325 0.0725 0.005)
(0.0375 0.0725 0.005)
(0.0425 0.0725 0.005)
(0.0475 0.0725 0.005)
(0.0525 0.0725 0.005)
(0.0575 0.0725 0.005)
(0.0625 0.0725 0.005)
(0.0675 0.0725 0.005)
(0.0725 0.0725 0.005)
(0.0775 0.0725 0.005)
(0.0825 0.0725 0.005)
(0.0875 0.0725 0.005)
(0.0925 0.0725 0.005)
(0.0975 0.0725 0.005)
(0.0025 0.0775 0.005)
(0.0075 0.0775 0.005)
(0.0125 0.0775 0.005)
(0.0175 0.0775 0.005)
(0.0225 0.0775 0.005)
(0.0275 0.0775 0.005)
(0.0325 0.0775 0.005)
(0.0375 0.0775 0.005)
(0.0425 0.0775 0.005)
(0.0475 0.0775 0.005)
(0.0525 0.0775 0.005)
(0.0575 0.0775 0.005)
(0.0625 0.0775 0.005)
(0.0675 0.0775 0.005)
(0.0725 0.0775 0.005)
(0.0775 0.0775 0.005)
(0.0825 0.0775 0.005)
(0.0875 0.0775 0.005)
(0.0925 0.0775 0.005)
(0.0975 0.0775 0.005)
(0.0025 0.0825 0.005)
(0.0075 0.0825 0.005)
(0.0125 0.0825 0.005)
(0.0175 0.0825 0.005)
(0.0225 0.0825 0.005)
(0.0275 0.0825 0.005)
(0.0325 0.0825 0.005)
(0.0375 0.0825 0.005)
(0.0425 0.0825 0.005)
(0.0475 0.0825 0.005)
(0.0525 0.0825 0.005)
(0.0575 0.0825 0.005)
(0.0625 0.0825 0.005)
(0.0675 0.0825 0.005)
(0.0725 0.0825 0.005)
(0.0775 0.0825 0.005)
(0.0825 0.0825 0.005)
(0.0875 0.0825 0.005)
(0.0925 0.0825 0.005)
(0.0975 0.0825 0.005)
(0.0025 0.0875 0.005)
(0.0075 0.0875 0.005)
(0.0125 0.0875 0.005)
(0.0175 0.0875 0.005)
(0.0225 0.0875 0.005)
(0.0275 0.0875 0.005)
(0.0325 0.0875 0.005)
(0.0375 0.0875 0.005)
(0.0425 0.0875 0.005)
(0.0475 0.0875 0.005)
(0.0525 0.0875 0.005)
(0.0575 0.0875 0.005)
(0.0625 0.0875 0.005)
(0.0675 0.0875 0.005)
(0.0725 0.0875 0.005)
(0.0775 0.0875 0.005)
(0.0825 0.0875 0.005)
(0.0875 0.0875 0.005)
(0.0925 0.0875 0.005)
(0.0975 0.0875 0.005)
(0.0025 0.0925 0.005)
(0.0075 0.0925 0.005)
(0.0125 0.0925 0.005)
(0.0175 0.0925 0.005)
(0.0225 0.0925 0.005)
(0.0275 0.0925 0.005)
(0.0325 0.0925 0.005)
(0.0375 0.0925 0.005)
(0.0425 0.0925 0.005)
(0.0475 0.0925 0.005)
(0.0525 0.0925 0.005)
(0.0575 0.0925 0.005)
(0.0625 0.0925 0.005)
(0.0675 0.0925 0.005)
(0.0725 0.0925 0.005)
(0.0775 0.0925 0.005)
(0.0825 0.0925 0.005)
(0.0875 0.0925 0.005)
(0.0925 0.0925 0.005)
(0.0975 0.0925 0.005)
(0.0025 0.0975 0.005)
(0.0075 0.0975 0.005)
(0.0125 0.0975 0.005)
(0.0175 0.0975 0.005)
(0.0225 0.0975 0.005)
(0.0275 0.0975 0.005)
(0.0325 0.0975 0.005)
(0.0375 0.0975 0.005)
(0.0425 0.0975 0.005)
(0.0475 0.0975 0.005)
(0.0525 0.0975 0.005)
(0.0575 0.0975 0.005)
(0.0625 0.0975 0.005)
(0.0675 0.0975 0.005)
(0.0725 0.0975 0.005)
(0.0775 0.0975 0.005)
(0.0825 0.0975 0.005)
(0.0875 0.0975 0.005)
(0.0925 0.0975 0.005)
(0.0975 0.0975 0.005)
)
;

boundaryField
{
    movingWall
    {
        type            calculated;
        value           nonuniform List<vector> 
20
(
(0.0025 0.1 0.005)
(0.0075 0.1 0.005)
(0.0125 0.1 0.005)
(0.0175 0.1 0.005)
(0.0225 0.1 0.005)
(0.0275 0.1 0.005)
(0.0325 0.1 0.005)
(0.0375 0.1 0.005)
(0.0425 0.1 0.005)
(0.0475 0.1 0.005)
(0.0525 0.1 0.005)
(0.0575 0.1 0.005)
(0.0625 0.1 0.005)
(0.0675 0.1 0.005)
(0.0725 0.1 0.005)
(0.0775 0.1 0.005)
(0.0825 0.1 0.005)
(0.0875 0.1 0.005)
(0.0925 0.1 0.005)
(0.0975 0.1 0.005)
)
;
    }
    fixedWalls
    {
        type            calculated;
        value           nonuniform List<vector> 
60
(
(0 0.0025 0.005)
(0 0.0075 0.005)
(0 0.0125 0.005)
(0 0.0175 0.005)
(0 0.0225 0.005)
(0 0.0275 0.005)
(0 0.0325 0.005)
(0 0.0375 0.005)
(0 0.0425 0.005)
(0 0.0475 0.005)
(0 0.0525 0.005)
(0 0.0575 0.005)
(0 0.0625 0.005)
(0 0.0675 0.005)
(0 0.0725 0.005)
(0 0.0775 0.005)
(0 0.0825 0.005)
(0 0.0875 0.005)
(0 0.0925 0.005)
(0 0.0975 0.005)
(0.1 0.0025 0.005)
(0.1 0.0075 0.005)
(0.1 0.0125 0.005)
(0.1 0.0175 0.005)
(0.1 0.0225 0.005)
(0.1 0.0275 0.005)
(0.1 0.0325 0.005)
(0.1 0.0375 0.005)
(0.1 0.0425 0.005)
(0.1 0.0475 0.005)
(0.1 0.0525 0.005)
(0.1 0.0575 0.005)
(0.1 0.0625 0.005)
(0.1 0.0675 0.005)
(0.1 0.0725 0.005)
(0.1 0.0775 0.005)
(0.1 0.0825 0.005)
(0.1 0.0875 0.005)
(0.1 0.0925 0.005)
(0.1 0.0975 0.005)
(0.0025 0 0.005)
(0.0075 0 0.005)
(0.0125 0 0.005)
(0.0175 0 0.005)
(0.0225 0 0.005)
(0.0275 0 0.005)
(0.0325 0 0.005)
(0.0375 0 0.005)
(0.0425 0 0.005)
(0.0475 0 0.005)
(0.0525 0 0.005)
(0.0575 0 0.005)
(0.0625 0 0.005)
(0.0675 0 0.005)
(0.0725 0 0.005)
(0.0775 0 0.005)
(0.0825 0 0.005)
(0.0875 0 0.005)
(0.0925 0 0.005)
(0.0975 0 0.005)
)
;
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
