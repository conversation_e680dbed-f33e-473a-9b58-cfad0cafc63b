/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2412                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volVectorField;
    location    "0.5";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   nonuniform List<vector> 
400
(
(0.000253405 -0.000250456 0)
(0.000141206 0.000111424 0)
(-0.00117704 0.000564623 0)
(-0.00348128 0.00088502 0)
(-0.0063778 0.00104575 0)
(-0.00946996 0.00106179 0)
(-0.0124036 0.000957892 0)
(-0.0148857 0.000760472 0)
(-0.0166904 0.000494665 0)
(-0.0176593 0.000184566 0)
(-0.0177031 -0.00014528 0)
(-0.016806 -0.000468393 0)
(-0.0150309 -0.000755454 0)
(-0.0125258 -0.000974315 0)
(-0.00952688 -0.00109125 0)
(-0.00635528 -0.00107406 0)
(-0.00340079 -0.000898167 0)
(-0.00108647 -0.000556415 0)
(0.000196445 -9.03668e-05 0)
(0.000267875 0.000262401 0)
(-7.7475e-05 -0.000169394 0)
(-0.00161166 0.0016449 0)
(-0.00569325 0.0036175 0)
(-0.0117196 0.00503512 0)
(-0.0188935 0.00574358 0)
(-0.0264025 0.00576057 0)
(-0.0334714 0.005172 0)
(-0.0394295 0.00409277 0)
(-0.043747 0.0026478 0)
(-0.0460503 0.000966972 0)
(-0.0461326 -0.000813297 0)
(-0.0439644 -0.00254726 0)
(-0.0397039 -0.00407893 0)
(-0.0337049 -0.00524546 0)
(-0.0265151 -0.00588695 0)
(-0.0188558 -0.00586523 0)
(-0.0115674 -0.00509507 0)
(-0.00551361 -0.00358963 0)
(-0.00149316 -0.00155921 0)
(-4.25773e-05 0.000227481 0)
(-0.000528648 0.00109168 0)
(-0.00371165 0.00573665 0)
(-0.0105897 0.0102251 0)
(-0.0199931 0.0134085 0)
(-0.030767 0.0149473 0)
(-0.0418253 0.0148398 0)
(-0.0521237 0.0132566 0)
(-0.0607487 0.0104569 0)
(-0.0669744 0.00674242 0)
(-0.0702879 0.00243898 0)
(-0.0704058 -0.00210675 0)
(-0.0672892 -0.00652598 0)
(-0.0611567 -0.01043 0)
(-0.0524922 -0.013422 0)
(-0.0420399 -0.0151245 0)
(-0.030776 -0.0152266 0)
(-0.0198304 -0.0135569 0)
(-0.0103642 -0.0101795 0)
(-0.00354873 -0.00555138 0)
(-0.000476871 -0.000948872 0)
(-0.000928295 0.00348007 0)
(-0.0056919 0.0122215 0)
(-0.0153029 0.019923 0)
(-0.0279188 0.0251995 0)
(-0.0419884 0.0275988 0)
(-0.0561831 0.0271393 0)
(-0.0692571 0.0241105 0)
(-0.0801315 0.0189551 0)
(-0.0879546 0.0121963 0)
(-0.0921254 0.00440527 0)
(-0.09231 -0.00380836 0)
(-0.0884564 -0.0117975 0)
(-0.0808105 -0.0188835 0)
(-0.0699277 -0.024378 0)
(-0.056673 -0.0276295 0)
(-0.0421976 -0.0281036 0)
(-0.0278584 -0.0254992 0)
(-0.0150946 -0.0198929 0)
(-0.00550537 -0.0119283 0)
(-0.000861651 -0.00322504 0)
(-0.00127902 0.00687824 0)
(-0.00753654 0.020955 0)
(-0.0198061 0.0325124 0)
(-0.0355372 0.0401057 0)
(-0.0527469 0.043293 0)
(-0.0698632 0.0421954 0)
(-0.0854691 0.0372799 0)
(-0.0983671 0.0292148 0)
(-0.107627 0.0187783 0)
(-0.112596 0.00681398 0)
(-0.112905 -0.00578096 0)
(-0.108481 -0.0180561 0)
(-0.0995685 -0.0290134 0)
(-0.0867445 -0.0376352 0)
(-0.07094 -0.0429477 0)
(-0.0534334 -0.0441315 0)
(-0.0357866 -0.040681 0)
(-0.0197362 -0.0325942 0)
(-0.007371 -0.0205917 0)
(-0.00120374 -0.00649848 0)
(-0.00160762 0.0112163 0)
(-0.00931853 0.0318764 0)
(-0.0242106 0.0479545 0)
(-0.0430049 0.0580752 0)
(-0.0632604 0.0619405 0)
(-0.0831573 0.0598844 0)
(-0.101129 0.0526311 0)
(-0.115898 0.0411279 0)
(-0.126495 0.0264383 0)
(-0.132244 0.00969316 0)
(-0.132756 -0.00791705 0)
(-0.127929 -0.0251338 0)
(-0.117972 -0.0406274 0)
(-0.103439 -0.0530223 0)
(-0.0852728 -0.0609694 0)
(-0.0648384 -0.0632832 0)
(-0.0438873 -0.0591447 0)
(-0.0244787 -0.0483465 0)
(-0.0092547 -0.0315589 0)
(-0.00153782 -0.010729 0)
(-0.00194013 0.0164779 0)
(-0.0111216 0.0449763 0)
(-0.0286514 0.0662363 0)
(-0.0504816 0.0790629 0)
(-0.0736875 0.08345 0)
(-0.0962071 0.0800777 0)
(-0.116356 0.0700274 0)
(-0.132819 0.0545871 0)
(-0.144636 0.035133 0)
(-0.151146 0.0130849 0)
(-0.151954 -0.0100873 0)
(-0.14692 -0.0328344 0)
(-0.136185 -0.0535023 0)
(-0.120227 -0.07034 0)
(-0.0999355 -0.0815687 0)
(-0.0767035 -0.0855361 0)
(-0.0524416 -0.0809668 0)
(-0.0295456 -0.0672794 0)
(-0.0112839 -0.0449411 0)
(-0.00190013 -0.0159521 0)
(-0.00230008 0.0226958 0)
(-0.0130253 0.060303 0)
(-0.0332514 0.0873666 0)
(-0.0580807 0.103 0)
(-0.0840761 0.107669 0)
(-0.108955 0.102565 0)
(-0.13097 0.0892472 0)
(-0.148837 0.0694131 0)
(-0.161669 0.044773 0)
(-0.168871 0.0170201 0)
(-0.170066 -0.0121344 0)
(-0.165074 -0.0408951 0)
(-0.153932 -0.067317 0)
(-0.136972 -0.0892765 0)
(-0.114944 -0.104516 0)
(-0.0891771 -0.1108 0)
(-0.0616715 -0.106216 0)
(-0.0351488 -0.089582 0)
(-0.013586 -0.0609512 0)
(-0.00232637 -0.0222782 0)
(-0.00271166 0.0299478 0)
(-0.0151081 0.0779585 0)
(-0.0381149 0.111359 0)
(-0.0658487 0.129753 0)
(-0.0943273 0.134321 0)
(-0.1211 0.126977 0)
(-0.144444 0.109905 0)
(-0.163214 0.0852839 0)
(-0.176688 0.0551683 0)
(-0.184408 0.0214871 0)
(-0.186059 -0.0138756 0)
(-0.181423 -0.0489584 0)
(-0.170396 -0.081596 0)
(-0.153082 -0.109327 0)
(-0.129972 -0.129384 0)
(-0.102186 -0.138832 0)
(-0.0716898 -0.13489 0)
(-0.0414671 -0.115472 0)
(-0.0162881 -0.079897 0)
(-0.00285471 -0.0298902 0)
(-0.00320765 0.0383603 0)
(-0.0174662 0.0980911 0)
(-0.043341 0.138192 0)
(-0.0737565 0.159049 0)
(-0.10416 0.162904 0)
(-0.132032 0.152676 0)
(-0.155826 0.131344 0)
(-0.174679 0.101642 0)
(-0.188168 0.0659588 0)
(-0.196068 0.0263898 0)
(-0.198186 -0.0151134 0)
(-0.194284 -0.056547 0)
(-0.184082 -0.0956444 0)
(-0.167367 -0.129692 0)
(-0.144216 -0.155427 0)
(-0.115339 -0.169101 0)
(-0.0824459 -0.166805 0)
(-0.0486361 -0.145126 0)
(-0.0195287 -0.102161 0)
(-0.00353357 -0.0390478 0)
(-0.00384394 0.0481292 0)
(-0.0202461 0.120895 0)
(-0.0490464 0.167754 0)
(-0.0816861 0.190369 0)
(-0.113053 0.19256 0)
(-0.140746 0.178618 0)
(-0.163621 0.152515 0)
(-0.181309 0.117595 0)
(-0.193846 0.0765361 0)
(-0.20136 0.0314946 0)
(-0.203856 -0.0156595 0)
(-0.201109 -0.0630519 0)
(-0.192648 -0.108493 0)
(-0.177857 -0.149173 0)
(-0.156224 -0.181418 0)
(-0.127779 -0.200609 0)
(-0.0936441 -0.201427 0)
(-0.056733 -0.178576 0)
(-0.0234798 -0.128174 0)
(-0.00443553 -0.0501049 0)
(-0.00472534 0.0595716 0)
(-0.0236945 0.146619 0)
(-0.0553826 0.199758 0)
(-0.0893759 0.222793 0)
(-0.120121 0.221895 0)
(-0.145675 0.203191 0)
(-0.165629 0.171842 0)
(-0.18037 0.131812 0)
(-0.19058 0.0859711 0)
(-0.196862 0.0363768 0)
(-0.199501 -0.0153711 0)
(-0.198335 -0.0677443 0)
(-0.192717 -0.118869 0)
(-0.181573 -0.166081 0)
(-0.163645 -0.205491 0)
(-0.137966 -0.231664 0)
(-0.104596 -0.237628 0)
(-0.0657334 -0.215527 0)
(-0.0283776 -0.158369 0)
(-0.00568207 -0.0635526 0)
(-0.00605154 0.0732287 0)
(-0.0282256 0.175564 0)
(-0.0625082 0.233572 0)
(-0.0962562 0.254749 0)
(-0.123856 0.248734 0)
(-0.144396 0.224029 0)
(-0.158669 0.187109 0)
(-0.168105 0.142464 0)
(-0.174199 0.0929701 0)
(-0.178117 0.0403795 0)
(-0.180483 -0.0141989 0)
(-0.181264 -0.0698223 0)
(-0.179694 -0.125212 0)
(-0.174269 -0.178182 0)
(-0.162894 -0.224975 0)
(-0.143316 -0.25959 0)
(-0.113937 -0.273331 0)
(-0.0753928 -0.255068 0)
(-0.0345562 -0.193091 0)
(-0.00748627 -0.0801046 0)
(-0.00819201 0.0900465 0)
(-0.0344825 0.208021 0)
(-0.0704116 0.267885 0)
(-0.101022 0.283608 0)
(-0.121573 0.269813 0)
(-0.133106 0.237843 0)
(-0.13816 0.195406 0)
(-0.13946 0.147235 0)
(-0.139364 0.0959102 0)
(-0.139592 0.04262 0)
(-0.141106 -0.0122333 0)
(-0.144052 -0.0685081 0)
(-0.147666 -0.125796 0)
(-0.150167 -0.182771 0)
(-0.148719 -0.236326 0)
(-0.139642 -0.280449 0)
(-0.119094 -0.305027 0)
(-0.0849355 -0.29516 0)
(-0.0424403 -0.232378 0)
(-0.010216 -0.100837 0)
(-0.0117674 0.111636 0)
(-0.0432594 0.243962 0)
(-0.0782965 0.300035 0)
(-0.100626 0.305094 0)
(-0.108349 0.280462 0)
(-0.105728 0.240384 0)
(-0.0975257 0.193283 0)
(-0.0877667 0.14355 0)
(-0.0794964 0.0930367 0)
(-0.0747806 0.0420953 0)
(-0.0747983 -0.00972904 0)
(-0.0798863 -0.0632119 0)
(-0.0894589 -0.118991 0)
(-0.101785 -0.176959 0)
(-0.113683 -0.235287 0)
(-0.120303 -0.288884 0)
(-0.115262 -0.327197 0)
(-0.0922658 -0.331795 0)
(-0.0523154 -0.275404 0)
(-0.0144303 -0.127347 0)
(-0.0175493 0.140659 0)
(-0.0547642 0.282146 0)
(-0.0827854 0.324719 0)
(-0.0880789 0.312475 0)
(-0.0751062 0.274457 0)
(-0.0525879 0.226772 0)
(-0.0274546 0.17729 0)
(-0.00451012 0.129136 0)
(0.0131037 0.0829134 0)
(0.023445 0.0379471 0)
(0.0254036 -0.00707371 0)
(0.0185123 -0.0537592 0)
(0.00298423 -0.103753 0)
(-0.0199931 -0.158321 0)
(-0.0476828 -0.217503 0)
(-0.0749824 -0.278362 0)
(-0.0935452 -0.331792 0)
(-0.0920629 -0.357683 0)
(-0.0632802 -0.319295 0)
(-0.0206335 -0.161974 0)
(-0.0254497 0.181711 0)
(-0.0657743 0.317771 0)
(-0.0733323 0.331363 0)
(-0.0480757 0.295401 0)
(-0.00557621 0.244213 0)
(0.0412213 0.192381 0)
(0.0846565 0.145048 0)
(0.120541 0.102972 0)
(0.146707 0.0651383 0)
(0.162052 0.0298916 0)
(0.165946 -0.00467174 0)
(0.15794 -0.0406289 0)
(0.137736 -0.0802473 0)
(0.105444 -0.125919 0)
(0.0622649 -0.179727 0)
(0.0118078 -0.242021 0)
(-0.0378286 -0.307878 0)
(-0.0716787 -0.360144 0)
(-0.0700552 -0.356609 0)
(-0.0279122 -0.208375 0)
(-0.029649 0.239176 0)
(-0.0608412 0.335768 0)
(-0.021942 0.300503 0)
(0.0505428 0.240128 0)
(0.127574 0.182949 0)
(0.19701 0.13539 0)
(0.254339 0.0973996 0)
(0.298254 0.0668515 0)
(0.328828 0.0413556 0)
(0.34647 0.0187787 0)
(0.351379 -0.00279427 0)
(0.343306 -0.0252654 0)
(0.321472 -0.0507882 0)
(0.284645 -0.0820486 0)
(0.231574 -0.122446 0)
(0.162182 -0.17567 0)
(0.0799015 -0.243432 0)
(-0.00318937 -0.319136 0)
(-0.0546694 -0.368612 0)
(-0.0284593 -0.267618 0)
(0.00808308 0.281861 0)
(0.0197831 0.294972 0)
(0.139066 0.210973 0)
(0.26446 0.143477 0)
(0.364368 0.0971862 0)
(0.440365 0.0657777 0)
(0.496744 0.0442377 0)
(0.536875 0.0288862 0)
(0.563448 0.017199 0)
(0.578307 0.00748481 0)
(0.582382 -0.00149417 0)
(0.575697 -0.0108454 0)
(0.55731 -0.0218472 0)
(0.525136 -0.0363351 0)
(0.475778 -0.0572598 0)
(0.404538 -0.0893842 0)
(0.305177 -0.139741 0)
(0.173417 -0.216386 0)
(0.0411721 -0.311655 0)
(0.0184302 -0.299995 0)
(0.299604 0.146091 0)
(0.396272 0.127503 0)
(0.55742 0.0760895 0)
(0.674487 0.0437233 0)
(0.743889 0.0255721 0)
(0.787154 0.0152422 0)
(0.815486 0.00921237 0)
(0.833956 0.00550944 0)
(0.845386 0.00302719 0)
(0.851372 0.00114935 0)
(0.852667 -0.000499394 0)
(0.849383 -0.00221509 0)
(0.841 -0.00433859 0)
(0.826141 -0.00742411 0)
(0.802078 -0.0125323 0)
(0.763621 -0.0217729 0)
(0.699108 -0.0392829 0)
(0.583952 -0.0726601 0)
(0.415827 -0.127868 0)
(0.308819 -0.149468 0)
)
;

boundaryField
{
    movingWall
    {
        type            fixedValue;
        value           uniform (1 0 0);
    }
    fixedWalls
    {
        type            fixedValue;
        value           uniform (0 0 0);
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
