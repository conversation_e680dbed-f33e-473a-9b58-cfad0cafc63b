/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2412                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volVectorField;
    location    "0.2";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   nonuniform List<vector> 
400
(
(0.000253424 -0.000250101 0)
(0.00014126 0.000111571 0)
(-0.00117663 0.00056449 0)
(-0.00348 0.000884708 0)
(-0.00637532 0.00104535 0)
(-0.00946616 0.00106137 0)
(-0.0123985 0.000957513 0)
(-0.0148796 0.000760173 0)
(-0.0166835 0.000494473 0)
(-0.017652 0.000184502 0)
(-0.0176959 -0.000145208 0)
(-0.0167992 -0.000468196 0)
(-0.0150249 -0.000755151 0)
(-0.0125209 -0.000973933 0)
(-0.00952329 -0.00109083 0)
(-0.00635302 -0.00107366 0)
(-0.00339971 -0.000897852 0)
(-0.00108627 -0.000556257 0)
(0.000196222 -9.04108e-05 0)
(0.000267721 0.000262231 0)
(-7.736e-05 -0.000168912 0)
(-0.0016108 0.00164454 0)
(-0.0056906 0.00361612 0)
(-0.0117142 0.00503302 0)
(-0.0188849 0.00574111 0)
(-0.0263905 0.00575807 0)
(-0.0334562 0.00516976 0)
(-0.0394116 0.00409102 0)
(-0.0437271 0.0026467 0)
(-0.0460295 0.000966632 0)
(-0.046112 -0.000812844 0)
(-0.0439448 -0.00254606 0)
(-0.0396864 -0.00407708 0)
(-0.0336903 -0.00524316 0)
(-0.0265038 -0.00588443 0)
(-0.0188478 -0.00586279 0)
(-0.0115626 -0.00509302 0)
(-0.00551143 -0.00358828 0)
(-0.00149264 -0.00155875 0)
(-4.25974e-05 0.000227234 0)
(-0.000528306 0.00109142 0)
(-0.00370966 0.00573427 0)
(-0.0105843 0.0102206 0)
(-0.0199833 0.0134025 0)
(-0.0307522 0.0149405 0)
(-0.0418052 0.0148331 0)
(-0.0520989 0.0132506 0)
(-0.06072 0.0104523 0)
(-0.0669429 0.00673955 0)
(-0.0702551 0.00243814 0)
(-0.0703732 -0.00210549 0)
(-0.0672584 -0.00652272 0)
(-0.061129 -0.010425 0)
(-0.0524686 -0.0134159 0)
(-0.0420212 -0.0151178 0)
(-0.0307624 -0.01522 0)
(-0.0198216 -0.0135511 0)
(-0.0103596 -0.0101753 0)
(-0.00354714 -0.00554921 0)
(-0.000476645 -0.00094862 0)
(-0.000927725 0.00347845 0)
(-0.00568888 0.0122158 0)
(-0.0152952 0.0199138 0)
(-0.0279053 0.0251879 0)
(-0.0419685 0.0275862 0)
(-0.0561567 0.0271269 0)
(-0.0692249 0.0240996 0)
(-0.0800945 0.0189467 0)
(-0.0879143 0.0121911 0)
(-0.0920836 0.0044038 0)
(-0.0922685 -0.00380598 0)
(-0.088417 -0.0117915 0)
(-0.0807749 -0.0188744 0)
(-0.0698972 -0.0243667 0)
(-0.0566484 -0.027617 0)
(-0.0421793 -0.0280912 0)
(-0.0278463 -0.0254882 0)
(-0.0150879 -0.0198844 0)
(-0.00550285 -0.0119233 0)
(-0.00086121 -0.0032237 0)
(-0.0012783 0.00687482 0)
(-0.00753284 0.0209451 0)
(-0.019797 0.0324974 0)
(-0.0355214 0.0400874 0)
(-0.0527239 0.0432734 0)
(-0.0698331 0.0421765 0)
(-0.0854327 0.0372633 0)
(-0.0983256 0.0292021 0)
(-0.107582 0.0187706 0)
(-0.112549 0.00681187 0)
(-0.112858 -0.00577728 0)
(-0.108437 -0.0180469 0)
(-0.0995285 -0.0289996 0)
(-0.0867101 -0.0376179 0)
(-0.070912 -0.0429286 0)
(-0.0534124 -0.0441123 0)
(-0.0357725 -0.0406637 0)
(-0.0197283 -0.0325805 0)
(-0.00736787 -0.0205829 0)
(-0.00120316 -0.00649562 0)
(-0.00160684 0.0112108 0)
(-0.00931462 0.0318617 0)
(-0.024201 0.0479332 0)
(-0.0429885 0.0580497 0)
(-0.0632367 0.0619136 0)
(-0.0831267 0.0598586 0)
(-0.101093 0.0526087 0)
(-0.115856 0.0411109 0)
(-0.126449 0.026428 0)
(-0.132197 0.00969041 0)
(-0.13271 -0.00791202 0)
(-0.127885 -0.0251214 0)
(-0.117932 -0.0406087 0)
(-0.103404 -0.0529989 0)
(-0.0852442 -0.0609435 0)
(-0.0648168 -0.063257 0)
(-0.0438726 -0.0591207 0)
(-0.0244703 -0.048327 0)
(-0.00925135 -0.0315461 0)
(-0.00153718 -0.0107244 0)
(-0.0019394 0.0164703 0)
(-0.011118 0.044957 0)
(-0.0286425 0.0662089 0)
(-0.0504665 0.0790306 0)
(-0.0736658 0.0834162 0)
(-0.0961791 0.0800455 0)
(-0.116322 0.0699996 0)
(-0.132781 0.0545659 0)
(-0.144595 0.0351203 0)
(-0.151104 0.0130816 0)
(-0.151912 -0.010081 0)
(-0.14688 -0.032819 0)
(-0.136149 -0.0534791 0)
(-0.120195 -0.070311 0)
(-0.0999092 -0.0815363 0)
(-0.0766835 -0.0855032 0)
(-0.0524278 -0.0809363 0)
(-0.0295377 -0.0672544 0)
(-0.0112807 -0.0449241 0)
(-0.00189952 -0.0159458 0)
(-0.00229949 0.0226864 0)
(-0.0130224 0.0602798 0)
(-0.0332443 0.0873339 0)
(-0.0580687 0.102962 0)
(-0.0840588 0.107629 0)
(-0.108933 0.102527 0)
(-0.130943 0.0892147 0)
(-0.148807 0.0693885 0)
(-0.161636 0.0447581 0)
(-0.168837 0.0170162 0)
(-0.170032 -0.0121271 0)
(-0.165042 -0.0408772 0)
(-0.153903 -0.0672901 0)
(-0.136946 -0.0892426 0)
(-0.114923 -0.104478 0)
(-0.0891607 -0.110761 0)
(-0.0616602 -0.10618 0)
(-0.0351422 -0.089552 0)
(-0.0135834 -0.0609305 0)
(-0.00232586 -0.0222703 0)
(-0.00271129 0.0299371 0)
(-0.0151062 0.0779324 0)
(-0.0381105 0.111322 0)
(-0.065841 0.129711 0)
(-0.0943163 0.134277 0)
(-0.121085 0.126935 0)
(-0.144427 0.109869 0)
(-0.163194 0.0852566 0)
(-0.176667 0.0551519 0)
(-0.184385 0.0214828 0)
(-0.186036 -0.0138676 0)
(-0.181401 -0.0489388 0)
(-0.170376 -0.0815663 0)
(-0.153064 -0.109289 0)
(-0.129957 -0.129342 0)
(-0.102175 -0.138789 0)
(-0.0716819 -0.13485 0)
(-0.0414625 -0.115438 0)
(-0.0162862 -0.0798735 0)
(-0.00285434 -0.0298811 0)
(-0.00320755 0.038349 0)
(-0.0174656 0.0980634 0)
(-0.0433397 0.138153 0)
(-0.0737542 0.159004 0)
(-0.104156 0.162858 0)
(-0.132027 0.152632 0)
(-0.15582 0.131306 0)
(-0.174672 0.101613 0)
(-0.18816 0.0659413 0)
(-0.19606 0.0263849 0)
(-0.198178 -0.0151053 0)
(-0.194275 -0.0565267 0)
(-0.184074 -0.0956134 0)
(-0.167359 -0.129653 0)
(-0.144209 -0.155383 0)
(-0.115334 -0.169055 0)
(-0.0824422 -0.166763 0)
(-0.0486339 -0.14509 0)
(-0.0195278 -0.102136 0)
(-0.00353337 -0.0390379 0)
(-0.00384413 0.048118 0)
(-0.020247 0.120867 0)
(-0.0490484 0.167716 0)
(-0.0816894 0.190324 0)
(-0.113057 0.192514 0)
(-0.140751 0.178574 0)
(-0.163627 0.152477 0)
(-0.181315 0.117566 0)
(-0.193853 0.0765183 0)
(-0.201367 0.0314894 0)
(-0.203863 -0.0156517 0)
(-0.201115 -0.0630319 0)
(-0.192653 -0.108463 0)
(-0.17786 -0.149134 0)
(-0.156226 -0.181374 0)
(-0.127781 -0.200564 0)
(-0.093645 -0.201384 0)
(-0.0567333 -0.178539 0)
(-0.0234799 -0.128148 0)
(-0.00443553 -0.0500948 0)
(-0.0047258 0.0595613 0)
(-0.0236967 0.146593 0)
(-0.0553878 0.199722 0)
(-0.0893845 0.222751 0)
(-0.120133 0.22185 0)
(-0.14569 0.203148 0)
(-0.165647 0.171806 0)
(-0.18039 0.131784 0)
(-0.190601 0.0859537 0)
(-0.196883 0.0363714 0)
(-0.199521 -0.0153641 0)
(-0.198354 -0.0677256 0)
(-0.192734 -0.11884 0)
(-0.181588 -0.166044 0)
(-0.163656 -0.205449 0)
(-0.137975 -0.23162 0)
(-0.104601 -0.237586 0)
(-0.0657363 -0.215492 0)
(-0.0283787 -0.158344 0)
(-0.00568225 -0.0635428 0)
(-0.00605222 0.0732198 0)
(-0.0282289 0.175542 0)
(-0.062516 0.23354 0)
(-0.0962692 0.254711 0)
(-0.123874 0.248694 0)
(-0.144419 0.22399 0)
(-0.158696 0.187076 0)
(-0.168136 0.142438 0)
(-0.174232 0.0929537 0)
(-0.17815 0.0403741 0)
(-0.180516 -0.014193 0)
(-0.181295 -0.0698057 0)
(-0.179722 -0.125186 0)
(-0.174293 -0.178149 0)
(-0.162913 -0.224937 0)
(-0.14333 -0.259551 0)
(-0.113946 -0.273293 0)
(-0.0753981 -0.255036 0)
(-0.0345582 -0.193069 0)
(-0.00748663 -0.0800956 0)
(-0.00819282 0.0900397 0)
(-0.0344864 0.208003 0)
(-0.070421 0.267858 0)
(-0.101038 0.283576 0)
(-0.121595 0.269779 0)
(-0.133134 0.23781 0)
(-0.138194 0.195377 0)
(-0.139498 0.147212 0)
(-0.139405 0.0958957 0)
(-0.139634 0.0426148 0)
(-0.141148 -0.0122287 0)
(-0.144092 -0.0684942 0)
(-0.147702 -0.125774 0)
(-0.150199 -0.182743 0)
(-0.148744 -0.236294 0)
(-0.139661 -0.280415 0)
(-0.119107 -0.304995 0)
(-0.0849427 -0.295132 0)
(-0.0424431 -0.232358 0)
(-0.0102165 -0.100829 0)
(-0.0117683 0.111631 0)
(-0.0432635 0.243949 0)
(-0.0783064 0.300015 0)
(-0.100643 0.305069 0)
(-0.108373 0.280435 0)
(-0.105759 0.240357 0)
(-0.0975628 0.193259 0)
(-0.0878087 0.143531 0)
(-0.0795419 0.0930245 0)
(-0.0748279 0.0420907 0)
(-0.0748453 -0.00972573 0)
(-0.0799312 -0.0632011 0)
(-0.0894999 -0.118974 0)
(-0.101821 -0.176936 0)
(-0.113713 -0.235261 0)
(-0.120325 -0.288857 0)
(-0.115277 -0.327171 0)
(-0.0922741 -0.331773 0)
(-0.0523187 -0.275388 0)
(-0.0144309 -0.127341 0)
(-0.01755 0.140657 0)
(-0.0547679 0.282137 0)
(-0.0827944 0.324705 0)
(-0.0880945 0.312457 0)
(-0.0751289 0.274437 0)
(-0.0526176 0.226752 0)
(-0.0274908 0.177271 0)
(-0.00455157 0.129122 0)
(0.0130584 0.082904 0)
(0.0233978 0.0379435 0)
(0.0253563 -0.00707148 0)
(0.0184669 -0.0537514 0)
(0.00294266 -0.10374 0)
(-0.0200293 -0.158305 0)
(-0.0477125 -0.217485 0)
(-0.075005 -0.278342 0)
(-0.0935605 -0.331773 0)
(-0.0920716 -0.357667 0)
(-0.0632836 -0.319283 0)
(-0.0206342 -0.16197 0)
(-0.0254502 0.18171 0)
(-0.065777 0.317767 0)
(-0.0733393 0.331354 0)
(-0.0480883 0.29539 0)
(-0.00559513 0.244199 0)
(0.0411959 0.192368 0)
(0.0846251 0.145036 0)
(0.120504 0.102963 0)
(0.146666 0.0651319 0)
(0.16201 0.029889 0)
(0.165903 -0.00467034 0)
(0.157899 -0.0406239 0)
(0.137698 -0.080239 0)
(0.105411 -0.125908 0)
(0.0622379 -0.179715 0)
(0.0117872 -0.242009 0)
(-0.0378426 -0.307866 0)
(-0.0716866 -0.360133 0)
(-0.0700584 -0.356602 0)
(-0.0279128 -0.208373 0)
(-0.0296491 0.239176 0)
(-0.0608425 0.335766 0)
(-0.0219462 0.300499 0)
(0.0505343 0.240122 0)
(0.12756 0.182942 0)
(0.196991 0.135382 0)
(0.254315 0.0973927 0)
(0.298225 0.0668461 0)
(0.328796 0.0413521 0)
(0.346437 0.0187773 0)
(0.351346 -0.00279346 0)
(0.343273 -0.0252626 0)
(0.321442 -0.0507837 0)
(0.284619 -0.0820429 0)
(0.231553 -0.12244 0)
(0.162166 -0.175663 0)
(0.0798906 -0.243425 0)
(-0.0031955 -0.319131 0)
(-0.0546717 -0.368609 0)
(-0.0284598 -0.267617 0)
(0.00808324 0.281862 0)
(0.0197829 0.294972 0)
(0.139065 0.210972 0)
(0.264456 0.143475 0)
(0.364361 0.0971835 0)
(0.440354 0.0657748 0)
(0.49673 0.0442351 0)
(0.536858 0.0288841 0)
(0.563428 0.0171977 0)
(0.578286 0.00748438 0)
(0.582361 -0.00149376 0)
(0.575677 -0.0108442 0)
(0.557291 -0.0218455 0)
(0.52512 -0.036333 0)
(0.475765 -0.0572574 0)
(0.404528 -0.0893819 0)
(0.30517 -0.139739 0)
(0.173414 -0.216384 0)
(0.0411709 -0.311654 0)
(0.0184301 -0.299995 0)
(0.299604 0.146091 0)
(0.396272 0.127503 0)
(0.55742 0.0760894 0)
(0.674487 0.043723 0)
(0.743887 0.0255717 0)
(0.78715 0.0152417 0)
(0.815481 0.00921193 0)
(0.833949 0.00550911 0)
(0.845379 0.00302701 0)
(0.851365 0.00114932 0)
(0.852659 -0.000499276 0)
(0.849376 -0.00221485 0)
(0.840994 -0.00433826 0)
(0.826136 -0.00742374 0)
(0.802074 -0.0125319 0)
(0.763618 -0.0217725 0)
(0.699106 -0.0392826 0)
(0.583952 -0.0726599 0)
(0.415827 -0.127868 0)
(0.30882 -0.149468 0)
)
;

boundaryField
{
    movingWall
    {
        type            fixedValue;
        value           uniform (1 0 0);
    }
    fixedWalls
    {
        type            fixedValue;
        value           uniform (0 0 0);
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
