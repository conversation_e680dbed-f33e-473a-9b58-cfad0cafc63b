/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2412                                  |
|   \\  /    A nd           | Website:  www.openfoam.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    arch        "LSB;label=32;scalar=64";
    class       volVectorField;
    location    "0.4";
    object      U;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 1 -1 0 0 0 0];

internalField   nonuniform List<vector> 
400
(
(0.000253396 -0.000250445 0)
(0.000141197 0.000111422 0)
(-0.00117701 0.000564605 0)
(-0.00348117 0.000884987 0)
(-0.00637758 0.00104571 0)
(-0.00946962 0.00106175 0)
(-0.0124032 0.000957852 0)
(-0.0148852 0.000760444 0)
(-0.0166898 0.000494651 0)
(-0.0176586 0.000184566 0)
(-0.0177025 -0.000145269 0)
(-0.0168054 -0.000468374 0)
(-0.0150304 -0.000755428 0)
(-0.0125254 -0.000974282 0)
(-0.00952658 -0.00109121 0)
(-0.0063551 -0.00107402 0)
(-0.0034007 -0.000898132 0)
(-0.00108645 -0.000556395 0)
(0.000196432 -9.03657e-05 0)
(0.000267866 0.00026239 0)
(-7.74713e-05 -0.000169384 0)
(-0.0016116 0.00164485 0)
(-0.00569306 0.00361737 0)
(-0.0117192 0.00503494 0)
(-0.0188928 0.00574337 0)
(-0.0264016 0.00576035 0)
(-0.0334702 0.0051718 0)
(-0.0394281 0.00409262 0)
(-0.0437454 0.00264771 0)
(-0.0460487 0.000966957 0)
(-0.046131 -0.000813246 0)
(-0.0439628 -0.00254715 0)
(-0.0397025 -0.00407877 0)
(-0.0337038 -0.00524527 0)
(-0.0265142 -0.00588675 0)
(-0.0188552 -0.00586503 0)
(-0.011567 -0.0050949 0)
(-0.00551346 -0.00358951 0)
(-0.00149313 -0.00155917 0)
(-4.25801e-05 0.000227467 0)
(-0.000528626 0.00109165 0)
(-0.00371152 0.00573645 0)
(-0.0105893 0.0102248 0)
(-0.0199924 0.0134081 0)
(-0.030766 0.0149467 0)
(-0.0418238 0.0148393 0)
(-0.0521218 0.0132561 0)
(-0.0607466 0.0104565 0)
(-0.0669721 0.00674219 0)
(-0.0702855 0.00243893 0)
(-0.0704034 -0.00210663 0)
(-0.067287 -0.0065257 0)
(-0.0611547 -0.0104296 0)
(-0.0524905 -0.0134215 0)
(-0.0420386 -0.015124 0)
(-0.030775 -0.0152261 0)
(-0.0198298 -0.0135564 0)
(-0.0103639 -0.0101792 0)
(-0.00354864 -0.00555122 0)
(-0.000476861 -0.000948851 0)
(-0.000928258 0.00347995 0)
(-0.0056917 0.012221 0)
(-0.0153024 0.0199223 0)
(-0.0279179 0.0251986 0)
(-0.0419871 0.0275979 0)
(-0.0561813 0.0271383 0)
(-0.0692548 0.0241097 0)
(-0.0801289 0.0189544 0)
(-0.0879517 0.0121959 0)
(-0.0921225 0.00440517 0)
(-0.092307 -0.00380815 0)
(-0.0884536 -0.011797 0)
(-0.0808081 -0.0188828 0)
(-0.0699256 -0.0243771 0)
(-0.0566713 -0.0276285 0)
(-0.0421963 -0.0281026 0)
(-0.0278576 -0.0254984 0)
(-0.0150942 -0.0198923 0)
(-0.00550522 -0.011928 0)
(-0.00086163 -0.00322494 0)
(-0.00127898 0.00687801 0)
(-0.00753631 0.0209543 0)
(-0.0198055 0.0325113 0)
(-0.0355362 0.0401044 0)
(-0.0527454 0.0432915 0)
(-0.0698612 0.042194 0)
(-0.0854666 0.0372786 0)
(-0.0983643 0.0292139 0)
(-0.107624 0.0187777 0)
(-0.112593 0.00681383 0)
(-0.112901 -0.00578066 0)
(-0.108478 -0.0180553 0)
(-0.0995658 -0.0290123 0)
(-0.0867422 -0.0376339 0)
(-0.0709382 -0.0429462 0)
(-0.0534321 -0.0441301 0)
(-0.0357857 -0.0406798 0)
(-0.0197357 -0.0325933 0)
(-0.00737082 -0.0205911 0)
(-0.00120371 -0.0064983 0)
(-0.00160757 0.0112159 0)
(-0.0093183 0.0318754 0)
(-0.02421 0.047953 0)
(-0.0430039 0.0580733 0)
(-0.0632588 0.0619385 0)
(-0.0831553 0.0598825 0)
(-0.101127 0.0526294 0)
(-0.115895 0.0411267 0)
(-0.126492 0.0264375 0)
(-0.132241 0.00969297 0)
(-0.132753 -0.00791665 0)
(-0.127926 -0.0251328 0)
(-0.117969 -0.040626 0)
(-0.103436 -0.0530205 0)
(-0.0852709 -0.0609675 0)
(-0.064837 -0.0632813 0)
(-0.0438864 -0.0591431 0)
(-0.0244782 -0.0483452 0)
(-0.0092545 -0.0315581 0)
(-0.00153778 -0.0107287 0)
(-0.00194009 0.0164774 0)
(-0.0111214 0.044975 0)
(-0.0286508 0.0662344 0)
(-0.0504806 0.0790606 0)
(-0.0736861 0.0834476 0)
(-0.0962053 0.0800753 0)
(-0.116354 0.0700254 0)
(-0.132817 0.0545856 0)
(-0.144633 0.0351321 0)
(-0.151144 0.0130847 0)
(-0.151951 -0.0100868 0)
(-0.146917 -0.0328333 0)
(-0.136183 -0.0535006 0)
(-0.120225 -0.0703379 0)
(-0.0999338 -0.0815664 0)
(-0.0767023 -0.0855338 0)
(-0.0524407 -0.0809647 0)
(-0.0295452 -0.0672778 0)
(-0.0112837 -0.0449401 0)
(-0.0019001 -0.0159518 0)
(-0.00230005 0.0226952 0)
(-0.0130251 0.0603015 0)
(-0.033251 0.0873644 0)
(-0.05808 0.102997 0)
(-0.084075 0.107666 0)
(-0.108954 0.102562 0)
(-0.130968 0.0892448 0)
(-0.148835 0.0694113 0)
(-0.161667 0.0447719 0)
(-0.168868 0.0170198 0)
(-0.170064 -0.0121338 0)
(-0.165072 -0.0408938 0)
(-0.15393 -0.0673151 0)
(-0.13697 -0.089274 0)
(-0.114943 -0.104513 0)
(-0.0891761 -0.110797 0)
(-0.0616709 -0.106213 0)
(-0.0351484 -0.08958 0)
(-0.0135859 -0.0609499 0)
(-0.00232634 -0.0222777 0)
(-0.00271164 0.0299471 0)
(-0.015108 0.0779568 0)
(-0.0381147 0.111356 0)
(-0.0658482 0.12975 0)
(-0.0943266 0.134318 0)
(-0.121099 0.126974 0)
(-0.144443 0.109902 0)
(-0.163213 0.0852819 0)
(-0.176687 0.0551672 0)
(-0.184406 0.0214869 0)
(-0.186057 -0.0138749 0)
(-0.181422 -0.048957 0)
(-0.170395 -0.0815938 0)
(-0.153081 -0.109324 0)
(-0.129971 -0.129381 0)
(-0.102186 -0.138829 0)
(-0.0716894 -0.134887 0)
(-0.0414669 -0.11547 0)
(-0.016288 -0.0798955 0)
(-0.00285469 -0.0298897 0)
(-0.00320765 0.0383596 0)
(-0.0174661 0.0980893 0)
(-0.0433409 0.138189 0)
(-0.0737564 0.159046 0)
(-0.10416 0.162901 0)
(-0.132032 0.152673 0)
(-0.155825 0.131341 0)
(-0.174678 0.10164 0)
(-0.188168 0.0659576 0)
(-0.196068 0.0263895 0)
(-0.198186 -0.0151128 0)
(-0.194283 -0.0565455 0)
(-0.184081 -0.0956421 0)
(-0.167366 -0.129689 0)
(-0.144215 -0.155424 0)
(-0.115338 -0.169097 0)
(-0.0824457 -0.166803 0)
(-0.048636 -0.145124 0)
(-0.0195287 -0.10216 0)
(-0.00353356 -0.0390472 0)
(-0.00384395 0.0481285 0)
(-0.0202461 0.120893 0)
(-0.0490465 0.167752 0)
(-0.0816863 0.190366 0)
(-0.113053 0.192557 0)
(-0.140746 0.178615 0)
(-0.163622 0.152512 0)
(-0.181309 0.117593 0)
(-0.193846 0.0765349 0)
(-0.201361 0.0314943 0)
(-0.203857 -0.0156588 0)
(-0.20111 -0.0630504 0)
(-0.192649 -0.108491 0)
(-0.177857 -0.149171 0)
(-0.156224 -0.181415 0)
(-0.127779 -0.200606 0)
(-0.0936442 -0.201424 0)
(-0.056733 -0.178573 0)
(-0.0234798 -0.128173 0)
(-0.00443553 -0.0501043 0)
(-0.00472536 0.059571 0)
(-0.0236947 0.146617 0)
(-0.0553829 0.199756 0)
(-0.0893764 0.22279 0)
(-0.120122 0.221891 0)
(-0.145676 0.203188 0)
(-0.165631 0.17184 0)
(-0.180371 0.13181 0)
(-0.190581 0.0859699 0)
(-0.196864 0.0363765 0)
(-0.199502 -0.0153705 0)
(-0.198336 -0.0677429 0)
(-0.192718 -0.118867 0)
(-0.181574 -0.166078 0)
(-0.163645 -0.205488 0)
(-0.137967 -0.231661 0)
(-0.104596 -0.237625 0)
(-0.0657335 -0.215525 0)
(-0.0283777 -0.158367 0)
(-0.00568208 -0.063552 0)
(-0.00605158 0.0732281 0)
(-0.0282258 0.175563 0)
(-0.0625087 0.23357 0)
(-0.096257 0.254746 0)
(-0.123857 0.248731 0)
(-0.144398 0.224026 0)
(-0.15867 0.187107 0)
(-0.168107 0.142462 0)
(-0.174201 0.0929689 0)
(-0.178119 0.0403792 0)
(-0.180485 -0.0141984 0)
(-0.181266 -0.0698211 0)
(-0.179696 -0.12521 0)
(-0.174271 -0.17818 0)
(-0.162895 -0.224973 0)
(-0.143316 -0.259588 0)
(-0.113938 -0.273329 0)
(-0.0753931 -0.255066 0)
(-0.0345563 -0.19309 0)
(-0.00748629 -0.0801041 0)
(-0.00819206 0.0900461 0)
(-0.0344827 0.20802 0)
(-0.0704122 0.267883 0)
(-0.101023 0.283606 0)
(-0.121574 0.269811 0)
(-0.133107 0.23784 0)
(-0.138162 0.195404 0)
(-0.139462 0.147233 0)
(-0.139367 0.0959092 0)
(-0.139595 0.0426196 0)
(-0.141109 -0.0122329 0)
(-0.144055 -0.068507 0)
(-0.147669 -0.125794 0)
(-0.150169 -0.182769 0)
(-0.14872 -0.236324 0)
(-0.139643 -0.280446 0)
(-0.119095 -0.305025 0)
(-0.084936 -0.295158 0)
(-0.0424405 -0.232377 0)
(-0.010216 -0.100837 0)
(-0.0117675 0.111635 0)
(-0.0432597 0.243961 0)
(-0.0782971 0.300034 0)
(-0.100628 0.305092 0)
(-0.108351 0.28046 0)
(-0.10573 0.240382 0)
(-0.0975282 0.193281 0)
(-0.0877695 0.143549 0)
(-0.0794994 0.0930358 0)
(-0.0747838 0.042095 0)
(-0.0748014 -0.00972875 0)
(-0.0798893 -0.0632111 0)
(-0.0894616 -0.11899 0)
(-0.101787 -0.176957 0)
(-0.113685 -0.235285 0)
(-0.120304 -0.288882 0)
(-0.115263 -0.327195 0)
(-0.0922663 -0.331794 0)
(-0.0523156 -0.275403 0)
(-0.0144303 -0.127347 0)
(-0.0175493 0.140659 0)
(-0.0547645 0.282145 0)
(-0.082786 0.324718 0)
(-0.0880799 0.312473 0)
(-0.0751077 0.274456 0)
(-0.0525899 0.22677 0)
(-0.0274571 0.177288 0)
(-0.00451295 0.129135 0)
(0.0131006 0.0829127 0)
(0.0234418 0.0379469 0)
(0.0254004 -0.00707349 0)
(0.0185092 -0.0537585 0)
(0.00298145 -0.103752 0)
(-0.0199955 -0.15832 0)
(-0.0476847 -0.217502 0)
(-0.0749839 -0.278361 0)
(-0.0935461 -0.331791 0)
(-0.0920634 -0.357682 0)
(-0.0632804 -0.319294 0)
(-0.0206336 -0.161974 0)
(-0.0254497 0.181711 0)
(-0.0657745 0.317771 0)
(-0.0733328 0.331362 0)
(-0.0480765 0.295401 0)
(-0.00557751 0.244212 0)
(0.0412195 0.19238 0)
(0.0846544 0.145047 0)
(0.120538 0.102972 0)
(0.146704 0.0651378 0)
(0.162049 0.0298914 0)
(0.165943 -0.00467159 0)
(0.157937 -0.0406285 0)
(0.137733 -0.0802466 0)
(0.105441 -0.125918 0)
(0.0622631 -0.179726 0)
(0.0118064 -0.24202 0)
(-0.0378295 -0.307878 0)
(-0.0716792 -0.360143 0)
(-0.0700554 -0.356608 0)
(-0.0279122 -0.208375 0)
(-0.029649 0.239176 0)
(-0.0608412 0.335767 0)
(-0.0219423 0.300503 0)
(0.0505422 0.240127 0)
(0.127573 0.182949 0)
(0.197008 0.135389 0)
(0.254337 0.097399 0)
(0.298251 0.0668511 0)
(0.328826 0.0413553 0)
(0.346468 0.0187786 0)
(0.351377 -0.00279419 0)
(0.343303 -0.0252652 0)
(0.32147 -0.0507878 0)
(0.284643 -0.0820481 0)
(0.231572 -0.122446 0)
(0.162181 -0.175669 0)
(0.0799008 -0.243431 0)
(-0.00318978 -0.319136 0)
(-0.0546695 -0.368612 0)
(-0.0284594 -0.267618 0)
(0.0080831 0.281861 0)
(0.0197831 0.294972 0)
(0.139066 0.210973 0)
(0.26446 0.143477 0)
(0.364368 0.097186 0)
(0.440364 0.0657775 0)
(0.496743 0.0442375 0)
(0.536874 0.028886 0)
(0.563447 0.0171989 0)
(0.578306 0.00748477 0)
(0.582381 -0.00149414 0)
(0.575696 -0.0108453 0)
(0.557309 -0.0218471 0)
(0.525135 -0.0363349 0)
(0.475777 -0.0572595 0)
(0.404537 -0.0893841 0)
(0.305176 -0.139741 0)
(0.173417 -0.216386 0)
(0.041172 -0.311655 0)
(0.0184302 -0.299995 0)
(0.299604 0.146091 0)
(0.396272 0.127503 0)
(0.55742 0.0760895 0)
(0.674487 0.0437232 0)
(0.743888 0.0255721 0)
(0.787153 0.0152422 0)
(0.815485 0.00921233 0)
(0.833955 0.00550941 0)
(0.845385 0.00302717 0)
(0.851371 0.00114934 0)
(0.852666 -0.000499391 0)
(0.849382 -0.00221507 0)
(0.840999 -0.00433856 0)
(0.82614 -0.00742407 0)
(0.802078 -0.0125322 0)
(0.763621 -0.0217728 0)
(0.699108 -0.0392829 0)
(0.583952 -0.0726601 0)
(0.415827 -0.127868 0)
(0.308819 -0.149468 0)
)
;

boundaryField
{
    movingWall
    {
        type            fixedValue;
        value           uniform (1 0 0);
    }
    fixedWalls
    {
        type            fixedValue;
        value           uniform (0 0 0);
    }
    frontAndBack
    {
        type            empty;
    }
}


// ************************************************************************* //
